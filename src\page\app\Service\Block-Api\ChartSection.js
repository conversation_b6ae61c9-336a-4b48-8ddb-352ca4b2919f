/* eslint-disable react/jsx-one-expression-per-line */
/* eslint-disable object-curly-newline */
/* eslint-disable function-paren-newline */
/* eslint-disable no-unused-vars */
/* eslint-disable object-curly-spacing */
import { Pie } from '@ant-design/plots';
import { Card, Col, Row, Tooltip } from 'antd';
import { useParams } from 'react-router-dom';
import React, { useEffect, useState } from 'react';
import { apiGenerator } from '../../../../util/functions';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import useHttp from '../../../../hooks/use-http';

const chartColor = [
    '#FF6B35',
    '#F7931E',
    '#FFB366',
    '#FFCC99',
    '#FF9999',
    '#FFD700',
];
const ChartSection = () => {
    // Calculate chart data from actual blocked IP statistics
    const { environmentID } = useParams();
    const api = useHttp();
    const [chartAnalysis, setChartAnalysis] = useState(
        [
            // { agent: "PostmanRuntime/7.44.1", totalExceedCount: "238" },
            // { agent: "sdf/7.44.1", totalExceedCount: "588" },
            // { agent: "PostmadsfdsnRuntime/7.44.1", totalExceedCount: "52" },
            // { agent: "dsf/7.44.1", totalExceedCount: "855" },
            // { agent: "PostmanRuntime/7.44.45", totalExceedCount: "415" },
            // { agent: "5445/7.44.1", totalExceedCount: "852" },
            // { agent: "gf/7.44.1", totalExceedCount: "123" },
            // { agent: "PostmanRgfhhgfuntime/7.44.1", totalExceedCount: "523" },
            // { agent: "PostmanRgfhgfgfuntime/7.44.1", totalExceedCount: "415" },
            // { agent: "54/7.44.1", totalExceedCount: "785" },
            // { agent: "4676576/7.44.1", totalExceedCount: "956" },
            // { agent: "fghb/7.44.1", totalExceedCount: "152" },
            // { agent: "gfhgfhgfh/7.44.1", totalExceedCount: "425" },
            // { agent: "gfhgfhgfhg/7.44.1", totalExceedCount: "635" },

        ]);

    useEffect(() => {
        api.sendRequest(apiGenerator(CONSTANTS.API.blockIP.getAnalytic, {
            serviceEnvironmentId: environmentID,
        }), (res) => {
            setChartAnalysis(res?.data);
        });
    }, []);
    const calculateChartData = () => {
        if (!chartAnalysis || chartAnalysis.length === 0) {
            return [];
        }

        // Calculate total exceed count for percentage calculation
        const totalExceedCount = chartAnalysis.reduce((sum, item) => sum + (+item.totalExceedCount || 0), 0);

        // Convert to chart data with colors using totalExceedCount
        const chartData = chartAnalysis?.map((item, index) => ({
            type: `${item?.agent || 'Unknown'}`,
            value: totalExceedCount > 0 ? Math.round(((item.totalExceedCount || 0) / totalExceedCount) * 100) : 0,
            count: item.totalExceedCount || 0,
            color: chartColor[index % chartColor.length],
        }));

        return chartData;
    };

    const chartData = calculateChartData();

    const config = {
        appendPadding: 10,
        data: chartData,
        angleField: 'value',
        colorField: 'type',
        radius: 1,
        innerRadius: 0.6,
        label: {
            type: 'inner',
            offset: '-50%',
            content: '{value}%',
            style: {
                textAlign: 'center',
                fontSize: 14,
                fontWeight: 'bold',
                fill: '#fff',
            },
        },
        color: (pr) => {
            const originalData = chartData?.find((item) => item.type === pr?.type);
            return originalData?.color;
        },
        tooltip: {
            formatter: (datum) => {
                // Find the original data to get totalExceedCount
                const originalData = chartAnalysis?.find((item) => item.agent === datum.type);
                return {
                    name: datum.type,
                    value: `${originalData?.totalExceedCount || 0}`,
                };
            },
        },
        interactions: [
            {
                type: 'element-selected',
            },
            {
                type: 'element-active',
            },
        ],
        statistic: {
            title: false,
            content: {
                style: {
                    whiteSpace: 'pre-wrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    // color: '#666',
                },
                content: 'Blocked\nRequests',
            },
        },
        legend: false,
    };
    return (
        <>{chartAnalysis?.length
            ? (
                <Card title="Blocked IP Statistics Overview">
                    <Row gutter={[24, 24]}>
                        <Col xs={24} sm={24} md={12} lg={10} xl={8}>
                            <div style={{ height: '300px' }}>
                                <Pie {...config} />
                            </div>
                        </Col>
                        <Col xs={24} sm={24} md={12} lg={14} xl={16}>
                            <div
                                className="flex flex-wrap gap-4"
                                style={{
                                    maxHeight: '300px',
                                    overflowY: chartData?.length > 6 ? 'auto' : 'visible',
                                    paddingRight: chartData?.length > 6 ? '8px' : '0',
                                }}
                            >
                                <Row gutter={[16, 16]} className="w-full">
                                    {chartData?.map((item) => {
                                        // Find the original data to get totalExceedCount
                                        const originalData = chartAnalysis?.find((data) => data?.agent === item?.type);

                                        return (
                                            <Col xs={12} sm={12} md={12} lg={8} xl={8} key={item?.type}>
                                                <Tooltip title={`${item?.type}`}>
                                                    {/* : ${originalData?.totalExceedCount} */}
                                                    <div className="flex items-center gap-3 p-3 border rounded-lg hover:shadow-md transition-shadow">
                                                        <div
                                                            className="w-4 h-4 rounded-full"
                                                            style={{ backgroundColor: item?.color }}
                                                        />
                                                        <div>
                                                            <div className="text-lg font-bold text-gray-800">
                                                                {item?.value}
                                                                %
                                                            </div>
                                                            <div className="text-sm text-gray-600" title={item?.type}>
                                                                {item?.type?.length > 8 ? `${item?.type?.substring(0, 8)}...` : item?.type}
                                                            </div>
                                                            {/* <div className="text-xs text-gray-500">
                                                        Exceed Count:
                                                        {' '}
                                                        {originalData?.totalExceedCount || 0}
                                                    </div> */}
                                                        </div>
                                                    </div>
                                                </Tooltip>
                                            </Col>
                                        );
                                    })}
                                </Row>
                            </div>
                        </Col>
                    </Row>
                </Card>
            ) : <></>}
        </>
    );
};

export default ChartSection;
