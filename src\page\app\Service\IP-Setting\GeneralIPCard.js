/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
import {
  Button, Card, Col, Row,
  Switch,
  Tooltip,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import ModalFormCreator from '../../../../component/common/ModalFormCreator';
import TooltipCustom from '../../../../component/common/Tooltip';
import { getAuthToken } from '../../../../util/API/authStorage';
import {
  apiGenerator, convertMillis, convertMillisValue, convertToMiliseconds,
} from '../../../../util/functions';
import useHttp from '../../../../hooks/use-http';
import CONSTANTS from '../../../../util/constant/CONSTANTS';

const GeneralIPCard = () => {
  const { projectID, serviceID, environmentID } = useParams();
  const [environment, setEnvironment] = useState([]);
  const [IsIPSettingDetails, setIsIPSettingDetails] = useState(false);
  const api = useHttp();
  useEffect(() => {
    if (getAuthToken() && environmentID) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.environment.getOneIP, {
          dataId: environmentID,
        }),
        (res) => {
          if (res?.status === "success") {
            setEnvironment(res?.data);
          }
        },
      );
    }
  }, [serviceID]);
  const generalIPSettingHandler = (value) => {
    const UPDATE_IP_SETTING_API = apiGenerator(CONSTANTS.API.environment.update, {
      dataId: environmentID,
    });
    // let milliseconds = 0;

    // Convert to milliseconds based on selected unit
    if (value?.windowMs) {
      value.windowMs = convertToMiliseconds(value?.windowMs, value?.windowMs_Unit);
      delete value?.windowMs_Unit;
    }
    if (value.blockDataSyncInterval) {
      value.blockDataSyncInterval = convertToMiliseconds(value?.blockDataSyncInterval, value?.blockDataSyncInterval_Unit);
      delete value?.blockDataSyncInterval_Unit;
    }
    if (value.autoReleaseAfter) {
      value.autoReleaseAfter = convertToMiliseconds(value?.autoReleaseAfter, value?.autoReleaseAfter_Unit);
      delete value?.autoReleaseAfter_Unit;
    }
    api.sendRequest(
      UPDATE_IP_SETTING_API,
      (res) => {
        setEnvironment((pr) => ({ ...pr, ...value }));
        setIsIPSettingDetails(false);
      },
      value,
      "Edit Setting Successfully!!!",
    );
  };
  const detials = [
    {
      id: 1,
      label: "Rate Limit",
      value: environment?.maxRequests || "-",
      tooltip: "Maximum number of requests allowed per key within the specified duration window",
    },
    {
      id: 2,
      label: "Allow Fault Attempt",
      value: (
        <p className="overflow-hidden break-all">
          {environment?.faultAllowLimit || "-"}
        </p>
      ),
      tooltip: "Number of rate limit violations after which an key will be permanently blocked",
    },
    {
      id: 4,
      label: "IP Key",
      value: environment?.ipKey || "-",
      tooltip: "Header or field name used to identify the client's key (e.g., 'x-forwarded-for', 'x-real-ip')",
    },
    {
      id: 3,
      label: "Duration",
      value: convertMillis(environment?.windowMs) || "-",
      tooltip: "Time window for counting requests. Rate limit resets after this duration",
    },
    {
      id: 10,
      label: "Block Data Sync Interval",
      value: convertMillis(environment?.blockDataSyncInterval),
      tooltip: "How often blocked IP data is synchronized across multiple server instances",
    },
    {
      id: 11,
      label: "Auto release after",
      value: convertMillis(environment?.autoReleaseAfter),
      tooltip: "Automatically unblock key after this duration. Set to 0 for permanent blocks",
    },
    {
      id: 5,
      label: "Rate Limit Excesses Message",
      value: environment?.rateLimitExceedErrMsg ?? "-",
      tooltip: "Custom error message shown to users when they exceed the rate limit",
    },
    {
      id: 6,
      label: "Block IP Message",
      value: environment?.blockIpMsg || "-",
      tooltip: "Custom error message shown to users when their key is blocked",
    },
    {
      id: 7,
      label: "Block after fault",
      value: environment?.isBlockAfterFault ? "Yes" : "No",
      type: "toggle",
      tooltip: "Automatically block key that repeatedly cause violate rate limit",
    },
    {
      id: 8,
      label: "Block if no key found",
      value: environment?.shouldBlockIfNoKeyFound ? "Yes" : "No",
      type: "toggle",
      tooltip: "Block requests when the specified IP key header is missing from the request",
    },
    // {
    //   id: 9,
    //   label: "Is Rate Limit",
    //   value: environment?.isRateLimit ? "Yes" : "No",
    //   type: "toggle",
    // },
  ];

  const ipModal = useMemo(() => {
    return CONSTANTS?.FORM_FIELD?.IP_SETTING_MODAL.map((ele) => {
      if (ele?.name === "windowMs" || ele?.name === "blockDataSyncInterval" || ele?.name === "autoReleaseAfter") {
        const { unit } = convertMillisValue(environment?.[ele?.name]);
        return {
          ...ele,
          defaultUnit: unit || "min",
          [`${ele?.name}_Unit`]: unit || "min",
        };
      }
      return {
        ...ele,
      };
    });
  }, [environment]);

  return (
    <>
      <Col span={24} lg={24} xl={24} xxl={24}>
        <Card title={(
          <div className="flex justify-between items-center">
            <h3>General IP Setting</h3>
            <div className="flex gap-3 items-center">
              <Tooltip title="Rate Limit">
                <Switch
                  checkedChildren="Enable"
                  unCheckedChildren="Disable"
                  loading={api.isLoading}
                  checked={environment?.isRateLimit || false}
                  onChange={(value) => {
                    generalIPSettingHandler({
                      isRateLimit: value,
                    });
                  }}
                />
              </Tooltip>
              <Button
                className="btn-dashboard-icon textcolor"
                type="primary"
                style={{ margin: "0px 5px" }}
                onClick={() => {
                  setIsIPSettingDetails(true);
                }}
              >
                Edit Endpoint Setting
              </Button>
            </div>
          </div>
        )}
        >
          {detials?.map((ele) => (
            <Row
              gutter={[16, 16]}
              className="mb-1.5 font-medium "
              key={ele?.id}
            >
              <Col span={8}>
                <div className="flex items-center">
                  {ele?.label}
                  &nbsp;:
                  {ele?.tooltip && (
                    <TooltipCustom
                      title={ele?.tooltip}
                      className="ml-1"
                    />
                  )}
                </div>
              </Col>
              <Col span={16}>{ele?.value}</Col>
            </Row>
          ))}
        </Card>
      </Col>
      {IsIPSettingDetails && (
        <ModalFormCreator
          open={IsIPSettingDetails}
          onCreate={generalIPSettingHandler}
          onCancel={() => {
            setIsIPSettingDetails((pr) => !pr);
          }}
          formFields={ipModal}
          name="Edit General IP Setting"
          // menu="IP_SETTING_MODAL"
          formData={{
            ...environment,
            windowMs: convertMillisValue(environment?.windowMs).value,
            windowMs_Unit: convertMillisValue(environment?.windowMs).unit,
            blockDataSyncInterval: convertMillisValue(environment?.blockDataSyncInterval).value,
            blockDataSyncInterval_Unit: convertMillisValue(environment?.blockDataSyncInterval).unit,
            autoReleaseAfter: convertMillisValue(environment?.autoReleaseAfter).value,
            autoReleaseAfter_Unit: convertMillisValue(environment?.autoReleaseAfter).unit,
          }}
        />
      )}
    </>
  );
};

export default GeneralIPCard;
