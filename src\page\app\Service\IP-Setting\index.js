/* eslint-disable padded-blocks */
/* eslint-disable no-use-before-define */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable import/order */
/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable no-unused-vars */
/* eslint-disable object-curly-newline */
import { Button, Card, Col, Pagination, Popconfirm, Row, Table, Typography } from 'antd';
import Search from 'antd/es/input/Search';
import React, { useEffect, useRef, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import useHttp from '../../../../hooks/use-http';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import ModalFormCreator from '../../../../component/common/ModalFormCreator';
import { apiGenerator, convertMillis, convertMillisValue, convertToMiliseconds, formatAmount, getMaxTimeUnit } from '../../../../util/functions';
import { useParams } from 'react-router-dom';
import { getAuthToken } from '../../../../util/API/authStorage';
import CRUDComponent from '../../../../component/common/CRUD-Component';
import GeneralIPCard from './GeneralIPCard';

const { Text } = Typography;

const IPSettingPage = (props) => {

    const api = useHttp();
    const [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 10,
        total: 0,
    });
    const { projectID, serviceID, environmentID } = useParams();

    return (
        <>
            <Row gutter={[16, 24]}>
                <GeneralIPCard />
                <Col span={24}>
                    <Card>

                        <CRUDComponent
                            // reload={refresh}
                            GET={{
                                API: CONSTANTS.API.rateLimitConfig.get,
                                extraQuery: {
                                    serviceEnvironmentId: environmentID,
                                },
                                DataModifier: (res, API, Setrefresh) => {
                                    return res?.map((ele, i) => {
                                        if (Object.keys(ele).includes("windowMs")) {
                                            const { unit } = convertMillisValue(ele?.windowMs);
                                        }
                                        return {
                                            ...ele,
                                            key: ele,
                                            no: i + 1,
                                            // ele?.id,
                                            method: ele?.method || "-",
                                            windowMs: +convertMillisValue(ele?.windowMs)?.value || "",
                                            windowMs_Unit: convertMillisValue(ele?.windowMs)?.unit || "min",
                                            // (ele?.windowMs / (60 * 1000))?.toFixed(0) || "-",
                                            isBlockAfterFaultTag: ele?.isBlockAfterFault ? "Yes" : "No",
                                            shouldBlockIfNoKeyFoundTag: ele?.shouldBlockIfNoKeyFound ? "Yes" : "No",
                                            windowMsTag: convertMillis(ele?.windowMs) || "-",
                                            // action: {
                                            //     Edit: true,
                                            //     Delete: true,
                                            // },
                                            endpointTag:
                                                <p className="overflow-hidden break-all">{ele?.endpoint}</p>
                                                || "-",
                                            // deleteWithReason: <Button onClick={() => setDeleteOpen(el?.id)} type="primary" className="textcolor">
                                            //     <DeleteOutlined />
                                            // </Button>,
                                        };
                                    });
                                },
                                column: CONSTANTS.TABLE.IPSETTINGHEADER,
                            }}
                            UPDATE={{
                                API: CONSTANTS.API.rateLimitConfig.update,
                                message: "Updated Endpoint IP Setting Successfully",
                                modaltitle: "Update Endpoint IP Setting",
                                modalFields: "ENDPOINT_IP_SETTING_MODAL",
                                // setRefresh,
                                payloadModifier: (res) => {
                                    res.windowMs = convertToMiliseconds(res?.windowMs, res?.windowMs_Unit ?? "min");
                                    delete res?.windowMs_Unit;
                                    return { ...res };
                                },
                            }}
                            CREATE={{
                                API: CONSTANTS.API.rateLimitConfig.add,
                                message: "Created Endpoint IP Setting Successfully",
                                modaltitle: "Add New Endpoint IP Setting",
                                modalFields: "ENDPOINT_IP_SETTING_MODAL",
                                name: "Add New Endpoint IP Setting",
                                // setRefresh,
                                payloadModifier: (res) => {
                                    res.windowMs = convertToMiliseconds(res?.windowMs, res?.windowMs_Unit ?? "min");
                                    delete res?.windowMs_Unit;
                                    return { ...res, serviceEnvironmentId: environmentID };
                                },
                            }}
                            DELETE={{
                                API: CONSTANTS.API.rateLimitConfig.delete,
                                message: "Deleted Successfully",
                                // setRefresh,
                            }}
                            isSearch
                        />
                    </Card>

                </Col>
            </Row>
            {/* <ModalFormCreator
                open={IsEndPointIPSettingDetails}
                onCreate={generalIPSettingHandler}
                onCancel={() => {
                    setIsEndPointIPSettingDetails((pr) => !pr);
                }}
                name="Add New Endpoint IP Setting"
                menu="ENDPOINT_IP_SETTING_MODAL"
            // formData={IPSettingDetails}
            /> */}
        </>
    );
};

export default IPSettingPage;
